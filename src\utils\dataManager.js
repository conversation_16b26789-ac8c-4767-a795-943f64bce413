/**
 * 数据管理器
 * 版本: v1.0.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v1.0.0: 初始版本，实现Excel文件读取、数据解析和管理功能
 */

class DataManager {
    constructor() {
        this.rawData = null;
        this.processedData = null;
        this.columns = [];
        this.fileName = '';
        this.fileSize = 0;
        this.rowCount = 0;
    }

    /**
     * 读取Excel文件
     * @param {File} file - Excel文件对象
     * @returns {Promise<Object>} 解析后的数据
     */
    async readExcelFile(file) {
        return new Promise((resolve, reject) => {
            try {
                this.fileName = file.name;
                this.fileSize = file.size;

                const reader = new FileReader();
                
                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });
                        
                        // 获取第一个工作表
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        
                        // 转换为JSON格式
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                            header: 1,
                            defval: null 
                        });
                        
                        this.rawData = jsonData;
                        this.processData();
                        
                        resolve({
                            success: true,
                            data: this.processedData,
                            columns: this.columns,
                            rowCount: this.rowCount,
                            fileName: this.fileName,
                            fileSize: this.fileSize
                        });
                        
                    } catch (error) {
                        reject({
                            success: false,
                            error: '文件解析失败: ' + error.message
                        });
                    }
                };
                
                reader.onerror = () => {
                    reject({
                        success: false,
                        error: '文件读取失败'
                    });
                };
                
                reader.readAsArrayBuffer(file);
                
            } catch (error) {
                reject({
                    success: false,
                    error: '文件处理失败: ' + error.message
                });
            }
        });
    }

    /**
     * 处理原始数据
     */
    processData() {
        if (!this.rawData || this.rawData.length === 0) {
            throw new Error('没有可处理的数据');
        }

        // 第一行作为列名
        this.columns = this.rawData[0] || [];
        
        // 剩余行作为数据
        const dataRows = this.rawData.slice(1);
        this.rowCount = dataRows.length;
        
        // 转换为对象数组格式
        this.processedData = dataRows.map((row, index) => {
            const rowData = { _rowIndex: index };
            this.columns.forEach((column, colIndex) => {
                rowData[column] = row[colIndex];
            });
            return rowData;
        });
    }

    /**
     * 获取指定列的数值数据（过滤掉非数值）
     * @param {string} columnName - 列名
     * @returns {Array<number>} 数值数组
     */
    getNumericColumn(columnName) {
        if (!this.processedData) {
            throw new Error('没有可用的数据');
        }

        return this.processedData
            .map(row => row[columnName])
            .filter(value => value !== null && value !== undefined && !isNaN(Number(value)))
            .map(value => Number(value));
    }

    /**
     * 获取所有数值列
     * @returns {Array<string>} 数值列名数组
     */
    getNumericColumns() {
        if (!this.processedData || this.processedData.length === 0) {
            return [];
        }

        return this.columns.filter(column => {
            const values = this.processedData
                .map(row => row[column])
                .filter(value => value !== null && value !== undefined);
            
            // 如果至少有一半的值是数值，则认为是数值列
            const numericValues = values.filter(value => !isNaN(Number(value)));
            return numericValues.length >= values.length * 0.5;
        });
    }

    /**
     * 获取数据预览（前10行）
     * @returns {Array<Object>} 预览数据
     */
    getPreviewData(maxRows = 10) {
        if (!this.processedData) {
            return [];
        }
        return this.processedData.slice(0, maxRows);
    }

    /**
     * 获取列的基本信息
     * @param {string} columnName - 列名
     * @returns {Object} 列信息
     */
    getColumnInfo(columnName) {
        if (!this.processedData) {
            throw new Error('没有可用的数据');
        }

        const allValues = this.processedData.map(row => row[columnName]);
        const values = allValues.filter(value => value !== null && value !== undefined && value !== '');
        const numericValues = values.filter(value => !isNaN(Number(value)));

        // 计算唯一值数量
        const uniqueValues = [...new Set(values)];

        // 推断数据类型
        const dataType = this.inferDataType(values);

        // 计算数据范围
        const dataRange = this.calculateDataRange(values, dataType);

        // 计算数据质量评分
        const qualityScore = this.calculateQualityScore(allValues, values, uniqueValues);

        // 建议用途
        const suggestedUsage = this.suggestUsage(dataType, uniqueValues.length, values.length);

        return {
            name: columnName,
            totalCount: this.processedData.length,
            validCount: values.length,
            nullCount: this.processedData.length - values.length,
            uniqueCount: uniqueValues.length,
            isNumeric: numericValues.length >= values.length * 0.5,
            numericCount: numericValues.length,
            dataType: dataType,
            dataTypeChinese: this.getChineseDataType(dataType),
            dataRange: dataRange,
            qualityScore: qualityScore,
            suggestedUsage: suggestedUsage
        };
    }

    /**
     * 推断数据类型
     * @param {Array} values - 值数组
     * @returns {string} 数据类型
     */
    inferDataType(values) {
        if (values.length === 0) return 'empty';

        const numericValues = values.filter(value => !isNaN(Number(value)));
        const numericRatio = numericValues.length / values.length;

        if (numericRatio >= 0.8) return 'numeric';
        if (numericRatio >= 0.5) return 'mixed';

        // 检查是否为日期
        const dateValues = values.filter(value => !isNaN(Date.parse(value)));
        if (dateValues.length >= values.length * 0.8) return 'date';

        // 检查是否为布尔值
        const booleanValues = values.filter(value => {
            const str = String(value).toLowerCase();
            return ['true', 'false', '是', '否', '1', '0', 'yes', 'no'].includes(str);
        });
        if (booleanValues.length >= values.length * 0.8) return 'boolean';

        return 'text';
    }

    /**
     * 获取中文数据类型
     * @param {string} dataType - 英文数据类型
     * @returns {string} 中文数据类型
     */
    getChineseDataType(dataType) {
        const typeMap = {
            'numeric': '数值型',
            'text': '文本型',
            'date': '日期型',
            'boolean': '布尔型',
            'mixed': '混合型',
            'empty': '空值型'
        };
        return typeMap[dataType] || '未知型';
    }

    /**
     * 计算数据范围
     * @param {Array} values - 值数组
     * @param {string} dataType - 数据类型
     * @returns {string} 数据范围描述
     */
    calculateDataRange(values, dataType) {
        if (values.length === 0) return '无数据';

        if (dataType === 'numeric') {
            const numericValues = values.map(v => Number(v)).filter(v => !isNaN(v));
            if (numericValues.length === 0) return '无有效数值';

            const min = Math.min(...numericValues);
            const max = Math.max(...numericValues);
            return `${min.toFixed(2)} ~ ${max.toFixed(2)}`;
        } else if (dataType === 'text') {
            const lengths = values.map(v => String(v).length);
            const minLength = Math.min(...lengths);
            const maxLength = Math.max(...lengths);
            return `${minLength} ~ ${maxLength} 字符`;
        } else if (dataType === 'date') {
            const dates = values.map(v => new Date(v)).filter(d => !isNaN(d));
            if (dates.length === 0) return '无有效日期';

            const minDate = new Date(Math.min(...dates));
            const maxDate = new Date(Math.max(...dates));
            return `${minDate.toLocaleDateString()} ~ ${maxDate.toLocaleDateString()}`;
        } else if (dataType === 'boolean') {
            const uniqueValues = [...new Set(values)];
            return uniqueValues.join(' / ');
        }

        return '混合类型';
    }

    /**
     * 计算数据质量评分
     * @param {Array} allValues - 所有值（包括空值）
     * @param {Array} validValues - 有效值
     * @param {Array} uniqueValues - 唯一值
     * @returns {number} 质量评分（0-100）
     */
    calculateQualityScore(allValues, validValues, uniqueValues) {
        // 完整性评分（40%权重）
        const completenessScore = (validValues.length / allValues.length) * 40;

        // 一致性评分（30%权重）
        const consistencyRatio = uniqueValues.length / validValues.length;
        let consistencyScore = 30;
        if (consistencyRatio > 0.8) consistencyScore = 30; // 高唯一性，好
        else if (consistencyRatio > 0.5) consistencyScore = 25; // 中等唯一性
        else if (consistencyRatio > 0.1) consistencyScore = 20; // 低唯一性，可能有重复
        else consistencyScore = 15; // 极低唯一性

        // 有效性评分（30%权重）
        let validityScore = 30;
        if (validValues.length === 0) validityScore = 0;
        else if (validValues.length < 10) validityScore = 15;
        else if (validValues.length < 100) validityScore = 25;

        return Math.round(completenessScore + consistencyScore + validityScore);
    }

    /**
     * 建议用途
     * @param {string} dataType - 数据类型
     * @param {number} uniqueCount - 唯一值数量
     * @param {number} validCount - 有效值数量
     * @returns {string} 建议用途
     */
    suggestUsage(dataType, uniqueCount, validCount) {
        if (validCount === 0) return '无法使用';

        const uniqueRatio = uniqueCount / validCount;

        if (dataType === 'numeric') {
            if (uniqueRatio > 0.8) return '连续变量';
            else if (uniqueRatio > 0.1) return '离散变量';
            else return '分类变量';
        } else if (dataType === 'text') {
            if (uniqueRatio > 0.9) return '标识符';
            else if (uniqueRatio > 0.1) return '分类变量';
            else return '标签变量';
        } else if (dataType === 'date') {
            return '时间变量';
        } else if (dataType === 'boolean') {
            return '二元变量';
        }

        return '混合用途';
    }

    /**
     * 清空数据
     */
    clear() {
        this.rawData = null;
        this.processedData = null;
        this.columns = [];
        this.fileName = '';
        this.fileSize = 0;
        this.rowCount = 0;
    }

    /**
     * 获取数据摘要
     * @returns {Object} 数据摘要
     */
    getSummary() {
        return {
            fileName: this.fileName,
            fileSize: this.fileSize,
            rowCount: this.rowCount,
            columnCount: this.columns.length,
            columns: this.columns,
            numericColumns: this.getNumericColumns(),
            hasData: this.processedData !== null && this.processedData.length > 0
        };
    }
}

// 导出数据管理器实例
window.dataManager = new DataManager();
