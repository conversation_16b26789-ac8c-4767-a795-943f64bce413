/**
 * 图表渲染器
 * 版本: v2.0.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v1.0.0: 初始版本，实现统计图表的渲染功能
 * - v2.0.0: 添加残差分析图表和增强的回归诊断图表
 */

class ChartRenderer {
    constructor() {
        this.Chart = window.Chart;
        this.chartInstances = new Map();
    }

    /**
     * 渲染直方图
     * @param {string} canvasId - 画布ID
     * @param {Object} frequencyData - 频率分布数据
     * @param {string} title - 图表标题
     */
    renderHistogram(canvasId, frequencyData, title = '频率分布') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const labels = frequencyData.binRanges.map(range => range.label);
        
        const chart = new this.Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '频率',
                    data: frequencyData.frequencies,
                    backgroundColor: 'rgba(59, 130, 246, 0.6)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '频率'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '数值区间'
                        }
                    }
                }
            }
        });

        this.chartInstances.set(canvasId, chart);
        return chart;
    }

    /**
     * 渲染散点图
     * @param {string} canvasId - 画布ID
     * @param {Array<number>} xData - X轴数据
     * @param {Array<number>} yData - Y轴数据
     * @param {string} title - 图表标题
     * @param {string} xLabel - X轴标签
     * @param {string} yLabel - Y轴标签
     * @param {Object} regressionLine - 回归线数据（可选）
     */
    renderScatterPlot(canvasId, xData, yData, title = '散点图', xLabel = 'X', yLabel = 'Y', regressionLine = null) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        // 准备散点数据
        const scatterData = [];
        for (let i = 0; i < Math.min(xData.length, yData.length); i++) {
            if (!isNaN(xData[i]) && !isNaN(yData[i])) {
                scatterData.push({ x: xData[i], y: yData[i] });
            }
        }

        const datasets = [{
            label: '数据点',
            data: scatterData,
            backgroundColor: 'rgba(59, 130, 246, 0.6)',
            borderColor: 'rgba(59, 130, 246, 1)',
            pointRadius: 4
        }];

        // 添加回归线
        if (regressionLine) {
            const minX = Math.min(...xData.filter(x => !isNaN(x)));
            const maxX = Math.max(...xData.filter(x => !isNaN(x)));
            
            const lineData = [
                { x: minX, y: regressionLine.slope * minX + regressionLine.intercept },
                { x: maxX, y: regressionLine.slope * maxX + regressionLine.intercept }
            ];

            datasets.push({
                label: '回归线',
                data: lineData,
                type: 'line',
                borderColor: 'rgba(239, 68, 68, 1)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                pointRadius: 0,
                borderWidth: 2,
                fill: false
            });
        }

        const chart = new this.Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: datasets
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: xLabel
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: yLabel
                        }
                    }
                }
            }
        });

        this.chartInstances.set(canvasId, chart);
        return chart;
    }

    /**
     * 渲染箱线图（使用条形图模拟）
     * @param {string} canvasId - 画布ID
     * @param {Object} stats - 描述统计数据
     * @param {string} title - 图表标题
     */
    renderBoxPlot(canvasId, stats, title = '箱线图') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const chart = new this.Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['最小值', 'Q1', '中位数', 'Q3', '最大值'],
                datasets: [{
                    label: '数值',
                    data: [stats.min, stats.quartiles.q1, stats.median, stats.quartiles.q3, stats.max],
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.6)',
                        'rgba(245, 158, 11, 0.6)',
                        'rgba(34, 197, 94, 0.6)',
                        'rgba(245, 158, 11, 0.6)',
                        'rgba(239, 68, 68, 0.6)'
                    ],
                    borderColor: [
                        'rgba(239, 68, 68, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(34, 197, 94, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    }
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '数值'
                        }
                    }
                }
            }
        });

        this.chartInstances.set(canvasId, chart);
        return chart;
    }

    /**
     * 渲染饼图
     * @param {string} canvasId - 画布ID
     * @param {Array} labels - 标签数组
     * @param {Array} data - 数据数组
     * @param {string} title - 图表标题
     */
    renderPieChart(canvasId, labels, data, title = '饼图') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const colors = [
            'rgba(59, 130, 246, 0.8)',
            'rgba(34, 197, 94, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(239, 68, 68, 0.8)',
            'rgba(168, 85, 247, 0.8)',
            'rgba(236, 72, 153, 0.8)',
            'rgba(14, 165, 233, 0.8)',
            'rgba(132, 204, 22, 0.8)'
        ];

        const chart = new this.Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        this.chartInstances.set(canvasId, chart);
        return chart;
    }

    /**
     * 销毁指定的图表
     * @param {string} canvasId - 画布ID
     */
    destroyChart(canvasId) {
        if (this.chartInstances.has(canvasId)) {
            this.chartInstances.get(canvasId).destroy();
            this.chartInstances.delete(canvasId);
        }
    }

    /**
     * 渲染残差vs拟合值散点图
     * @param {string} canvasId - 画布ID
     * @param {Array<number>} fittedValues - 拟合值数组
     * @param {Array<number>} residuals - 残差数组
     * @param {string} title - 图表标题
     */
    renderResidualPlot(canvasId, fittedValues, residuals, title = '残差vs拟合值') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        // 准备散点数据
        const scatterData = [];
        for (let i = 0; i < Math.min(fittedValues.length, residuals.length); i++) {
            if (!isNaN(fittedValues[i]) && !isNaN(residuals[i])) {
                scatterData.push({ x: fittedValues[i], y: residuals[i] });
            }
        }

        // 添加零线
        const minX = Math.min(...fittedValues);
        const maxX = Math.max(...fittedValues);
        const zeroLineData = [
            { x: minX, y: 0 },
            { x: maxX, y: 0 }
        ];

        const chart = new this.Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: '残差',
                    data: scatterData,
                    backgroundColor: 'rgba(59, 130, 246, 0.6)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    pointRadius: 4
                }, {
                    label: '零线',
                    data: zeroLineData,
                    type: 'line',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    pointRadius: 0,
                    borderWidth: 2,
                    fill: false,
                    borderDash: [5, 5]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '拟合值'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '残差'
                        }
                    }
                }
            }
        });

        this.chartInstances.set(canvasId, chart);
        return chart;
    }

    /**
     * 渲染标准化残差图
     * @param {string} canvasId - 画布ID
     * @param {Array<number>} fittedValues - 拟合值数组
     * @param {Array<number>} standardizedResiduals - 标准化残差数组
     * @param {string} title - 图表标题
     */
    renderStandardizedResidualPlot(canvasId, fittedValues, standardizedResiduals, title = '标准化残差图') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        // 准备散点数据
        const scatterData = [];
        for (let i = 0; i < Math.min(fittedValues.length, standardizedResiduals.length); i++) {
            if (!isNaN(fittedValues[i]) && !isNaN(standardizedResiduals[i])) {
                scatterData.push({ x: fittedValues[i], y: standardizedResiduals[i] });
            }
        }

        // 添加参考线
        const minX = Math.min(...fittedValues);
        const maxX = Math.max(...fittedValues);
        const referenceLines = [
            { data: [{ x: minX, y: 0 }, { x: maxX, y: 0 }], color: 'rgba(239, 68, 68, 1)', label: '零线' },
            { data: [{ x: minX, y: 2 }, { x: maxX, y: 2 }], color: 'rgba(245, 158, 11, 1)', label: '+2σ' },
            { data: [{ x: minX, y: -2 }, { x: maxX, y: -2 }], color: 'rgba(245, 158, 11, 1)', label: '-2σ' }
        ];

        const datasets = [{
            label: '标准化残差',
            data: scatterData,
            backgroundColor: 'rgba(59, 130, 246, 0.6)',
            borderColor: 'rgba(59, 130, 246, 1)',
            pointRadius: 4
        }];

        // 添加参考线
        referenceLines.forEach((line, index) => {
            datasets.push({
                label: line.label,
                data: line.data,
                type: 'line',
                borderColor: line.color,
                backgroundColor: 'transparent',
                pointRadius: 0,
                borderWidth: 1,
                fill: false,
                borderDash: index === 0 ? [5, 5] : [3, 3]
            });
        });

        const chart = new this.Chart(ctx, {
            type: 'scatter',
            data: { datasets: datasets },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: title
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '拟合值'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '标准化残差'
                        }
                    }
                }
            }
        });

        this.chartInstances.set(canvasId, chart);
        return chart;
    }

    /**
     * 渲染Q-Q图（正态性检验）
     * @param {string} canvasId - 画布ID
     * @param {Array<number>} residuals - 残差数组
     * @param {string} title - 图表标题
     */
    renderQQPlot(canvasId, residuals, title = '残差正态Q-Q图') {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            throw new Error(`找不到画布元素: ${canvasId}`);
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        // 数据验证和预处理
        if (!residuals || residuals.length === 0) {
            throw new Error('残差数据为空');
        }

        // 过滤有效数据
        const validResiduals = residuals.filter(r =>
            r !== null && r !== undefined && !isNaN(r) && isFinite(r)
        );

        if (validResiduals.length < 3) {
            throw new Error('有效残差数据不足，无法生成Q-Q图');
        }

        // 计算理论分位数和样本分位数
        const sortedResiduals = [...validResiduals].sort((a, b) => a - b);
        const n = sortedResiduals.length;



        const qqData = [];
        const theoreticalQuantiles = [];
        const sampleQuantiles = [];

        try {
            for (let i = 0; i < n; i++) {
                // 使用更稳定的概率计算方法
                const p = (i + 0.375) / (n + 0.25); // Blom plotting position
                const theoreticalQuantile = this.normalQuantile(p);
                const sampleQuantile = sortedResiduals[i];

                if (isFinite(theoreticalQuantile) && isFinite(sampleQuantile)) {
                    qqData.push({ x: theoreticalQuantile, y: sampleQuantile });
                    theoreticalQuantiles.push(theoreticalQuantile);
                    sampleQuantiles.push(sampleQuantile);
                }
            }
        } catch (error) {
            throw new Error('计算Q-Q数据时出错: ' + error.message);
        }

        if (qqData.length === 0) {
            throw new Error('Q-Q数据计算失败');
        }

        // 计算参考线范围
        const minTheoreticalQ = Math.min(...theoreticalQuantiles);
        const maxTheoreticalQ = Math.max(...theoreticalQuantiles);
        const minSampleQ = Math.min(...sampleQuantiles);
        const maxSampleQ = Math.max(...sampleQuantiles);

        // 扩展范围以确保参考线覆盖所有数据点
        const rangeExtension = 0.1;
        const xRange = maxTheoreticalQ - minTheoreticalQ;
        const yRange = maxSampleQ - minSampleQ;

        const xMin = minTheoreticalQ - xRange * rangeExtension;
        const xMax = maxTheoreticalQ + xRange * rangeExtension;
        const yMin = minSampleQ - yRange * rangeExtension;
        const yMax = maxSampleQ + yRange * rangeExtension;

        // 参考线应该覆盖数据的完整范围
        const overallMin = Math.min(xMin, yMin);
        const overallMax = Math.max(xMax, yMax);

        const referenceLine = [
            { x: overallMin, y: overallMin },
            { x: overallMax, y: overallMax }
        ];



        try {
            const chart = new this.Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Q-Q点',
                        data: qqData,
                        backgroundColor: 'rgba(59, 130, 246, 0.6)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }, {
                        label: '理论正态线',
                        data: referenceLine,
                        type: 'line',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        backgroundColor: 'transparent',
                        pointRadius: 0,
                        borderWidth: 2,
                        fill: false,
                        borderDash: [5, 5]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: title,
                            font: {
                                size: 14
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    if (context.datasetIndex === 0) {
                                        return `理论: ${context.parsed.x.toFixed(3)}, 样本: ${context.parsed.y.toFixed(3)}`;
                                    }
                                    return context.dataset.label;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '理论分位数',
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '样本分位数',
                                font: {
                                    size: 12
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'point'
                    }
                }
            });

            this.chartInstances.set(canvasId, chart);
            return chart;

        } catch (error) {
            throw new Error('创建Q-Q图表失败: ' + error.message);
        }
    }

    /**
     * 计算标准正态分布的分位数（近似）
     * @param {number} p - 概率值 (0 < p < 1)
     * @returns {number} 分位数
     */
    normalQuantile(p) {
        // 边界检查
        if (p <= 0 || p >= 1) {
            throw new Error('概率值必须在0和1之间');
        }

        if (p === 0.5) return 0;

        // 使用Box-Muller变换的逆函数近似
        // 这是一个更简单但足够准确的实现
        if (p < 0.5) {
            // 对于p < 0.5，计算-normalQuantile(1-p)
            return -this.normalQuantile(1 - p);
        }

        // 使用Acklam算法的简化版本
        const a0 = -3.969683028665376e+01;
        const a1 = 2.209460984245205e+02;
        const a2 = -2.759285104469687e+02;
        const a3 = 1.383577518672690e+02;
        const a4 = -3.066479806614716e+01;
        const a5 = 2.506628277459239e+00;

        const b1 = -5.447609879822406e+01;
        const b2 = 1.615858368580409e+02;
        const b3 = -1.556989798598866e+02;
        const b4 = 6.680131188771972e+01;
        const b5 = -1.328068155288572e+01;

        const c0 = -7.784894002430293e-03;
        const c1 = -3.223964580411365e-01;
        const c2 = -2.400758277161838e+00;
        const c3 = -2.549732539343734e+00;
        const c4 = 4.374664141464968e+00;
        const c5 = 2.938163982698783e+00;

        const d1 = 7.784695709041462e-03;
        const d2 = 3.224671290700398e-01;
        const d3 = 2.445134137142996e+00;
        const d4 = 3.754408661907416e+00;

        // 定义分界点
        const p_low = 0.02425;
        const p_high = 1 - p_low;

        let x;

        if (p < p_low) {
            // 尾部区域
            const q = Math.sqrt(-2 * Math.log(p));
            x = (((((c5 * q + c4) * q + c3) * q + c2) * q + c1) * q + c0) /
                ((((d4 * q + d3) * q + d2) * q + d1) * q + 1);
        } else if (p <= p_high) {
            // 中心区域
            const q = p - 0.5;
            const r = q * q;
            x = (((((a5 * r + a4) * r + a3) * r + a2) * r + a1) * r + a0) * q /
                (((((b5 * r + b4) * r + b3) * r + b2) * r + b1) * r + 1);
        } else {
            // 上尾部区域
            const q = Math.sqrt(-2 * Math.log(1 - p));
            x = -(((((c5 * q + c4) * q + c3) * q + c2) * q + c1) * q + c0) /
                 ((((d4 * q + d3) * q + d2) * q + d1) * q + 1);
        }

        return x;
    }

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        this.chartInstances.forEach(chart => chart.destroy());
        this.chartInstances.clear();
    }

    /**
     * 创建画布元素
     * @param {string} canvasId - 画布ID
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @returns {HTMLCanvasElement} 画布元素
     */
    createCanvas(canvasId, width = 400, height = 300) {
        const canvas = document.createElement('canvas');
        canvas.id = canvasId;
        canvas.width = width;
        canvas.height = height;
        canvas.className = 'max-w-full h-auto';
        return canvas;
    }

    /**
     * 创建图表容器
     * @param {string} containerId - 容器ID
     * @param {string} title - 标题
     * @returns {HTMLDivElement} 容器元素
     */
    createChartContainer(containerId, title) {
        const container = document.createElement('div');
        container.id = containerId;
        container.className = 'bg-gray-50 p-3 sm:p-4 rounded-lg border mb-4';

        const titleElement = document.createElement('h4');
        titleElement.className = 'text-sm sm:text-base font-semibold text-gray-900 mb-3';
        titleElement.textContent = title;

        const canvasContainer = document.createElement('div');
        canvasContainer.className = 'chart-container';

        container.appendChild(titleElement);
        container.appendChild(canvasContainer);

        return { container, canvasContainer };
    }
}

// 导出图表渲染器实例
window.chartRenderer = new ChartRenderer();
