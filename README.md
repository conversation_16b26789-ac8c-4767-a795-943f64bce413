# 回归分析工具 - 纯前端统计分析应用

**版本**: v3.0.0
**创建日期**: 2025-07-19
**最后更新**: 2025-07-30
**变更记录**:
- v3.0.0: 项目代码清理和优化，删除多余文件和调试代码，提升生产环境性能

## 项目简介

这是一个纯前端的统计分析应用，专门用于Excel数据的统计分析。应用采用极简紧凑的单列垂直布局设计，集成所有功能模块在单一页面中，提供流畅的用户操作体验，适合数据分析新手使用。

## 界面特点

- **单页面设计**: 所有功能集成在一个页面，无需跳转
- **垂直流程布局**: 按照用户操作流程从上到下依次排列
- **完全响应式**: 适配桌面、平板和移动设备
- **深度空间优化**: 累计提升空间利用率35%，信息密度提升70%
- **固定尺寸表格**: 120px×32px单元格，确保布局一致性
- **水平统计布局**: 统计表格横向展示，便于变量间比较
- **智能变量选择**: 用户可自主选择分析变量，精确控制分析范围
- **通俗化解释**: 统计指标配备易懂解释，零基础用户也能理解
- **个性化说明**: 根据实际变量生成具体的业务解释
- **表格化列信息**: 8维度数据质量评估，中文化类型显示
- **智能质量评分**: 0-100分数据质量评分和用途建议
- **增强相关性可视化**: 基于Chart.js的交互式图表系统，包含条形图、热力图和散点图
- **真正的矩阵热力图**: 原生HTML/CSS实现的方形单元格热力图，替代圆形点模拟
- **标签页式界面**: 直观的标签页导航，支持多种可视化视图快速切换

## 核心功能

### 1. 数据导入
- 支持拖拽上传Excel文件（.xlsx/.xls格式）
- 自动解析数据并识别数值列
- 实时数据预览和概览统计

### 2. 描述统计
- **基本统计量**: 均值、中位数、众数、标准差、方差等
- **分布分析**: 最小值、最大值、四分位数、偏度、峰度
- **频率分布**: 直方图和分组频率分析
- **可视化**: 箱线图、直方图等

### 3. 推断统计
- **增强相关性分析**:
  - 📊 相关强度条形图：按强度排序的交互式条形图
  - 🔥 交互式热力图：颜色编码的相关系数矩阵
  - 📈 增强散点图：包含趋势线的散点图矩阵
  - 智能颜色编码和悬停提示
- **t检验**: 
  - 单样本t检验
  - 显著性检验结果解释
- **增强回归分析**:
  - 详细的回归系数分析（估计值、标准误、t值、p值、置信区间）
  - 专业统计表格（系数汇总、模型摘要、方差分析表）
  - 残差分析和诊断（正态性检验、异常值检测、自相关检验）
  - 诊断图表（残差图、Q-Q图、标准化残差图）
  - 模型假设验证和结果解释

## 技术特点

- **纯前端实现**: 无需后端服务器，数据处理完全在浏览器中完成
- **现代技术栈**: 
  - HTML5 + JavaScript (ES6+)
  - TailwindCSS 3.0+ (CDN)
  - Chart.js 4.4+ (图表渲染)
  - SheetJS (Excel文件解析)
  - Simple-statistics (统计计算)
- **响应式设计**: 适配桌面和移动设备
- **实时计算**: 即时显示分析结果

## 快速开始

### 1. 环境要求
- 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- 支持ES6+和Web APIs

### 2. 安装和运行

#### 方法一：直接打开
```bash
# 克隆或下载项目
git clone [项目地址]
cd regression-analysis-app

# 直接在浏览器中打开
open index.html
```

#### 方法二：使用开发服务器
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
# 或者
npx live-server --port=3000
```

#### 方法三：使用Python服务器
```bash
# Python 3
python -m http.server 8000

# 访问 http://localhost:8000
```

### 3. 使用步骤

1. **上传数据**: 拖拽Excel文件到上传区域或点击选择文件
2. **查看概览**: 自动显示数据概览和列信息
3. **选择变量**: 选择要分析的变量，回归分析需要指定自变量和因变量
4. **选择分析**: 勾选需要的分析类型，或点击"快速分析"
5. **查看结果**: 分析结果包含统计表格和可视化图表
6. **导出结果**: 可以截图保存或复制数据

## 项目结构

```
regression-analysis-app/
├── index.html              # 主应用页面（重构后的单列布局）
├── package.json            # 项目配置和依赖
├── README.md              # 项目说明文档
├── PROJECT_SUMMARY.md      # 项目完成总结
├── ENHANCED_REGRESSION_FEATURES.md # 增强回归分析功能说明
├── INTERFACE_REFACTOR_SUMMARY.md # 界面重构总结
├── src/                   # 源代码目录
│   ├── app.js            # 主应用逻辑（支持增强回归分析）
│   ├── utils/            # 工具类
│   │   ├── dataManager.js        # 数据管理器
│   │   ├── statisticsCalculator.js # 统计计算器（增强回归功能）
│   │   └── chartRenderer.js      # 图表渲染器（残差分析图表）
│   └── tests/            # 测试文件
│       ├── dataManager.test.js
│       ├── statisticsCalculator.test.js
│       ├── enhancedRegression.test.js # 增强回归分析测试
│       ├── variableSelection.test.js  # 变量选择功能测试
│       └── qqPlot.test.js            # Q-Q图功能测试
└── node_modules/         # 依赖包（开发环境）
```

## 功能演示

### 数据导入
- 支持拖拽上传
- 文件格式验证
- 实时解析进度

### 数据概览
- 数据集基本信息
- 列类型自动识别
- 缺失值统计

### 统计分析
- 一键快速分析
- 分模块选择分析
- 实时结果展示

### 可视化图表
- 直方图（频率分布）
- 箱线图（数据分布）
- 散点图（相关性）
- 回归线图（回归分析）

## 测试

### 运行测试
```bash
# 使用Jest运行单元测试
npm test
```

### 测试内容
- 数据管理器功能测试
- 统计计算器功能测试
- 增强回归分析测试
- 变量选择功能测试
- Q-Q图功能测试

## 浏览器兼容性

| 浏览器 | 最低版本 | 状态 |
|--------|----------|------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |
| IE | - | ❌ 不支持 |

## 性能优化

- 大数据集分批处理
- 图表懒加载
- 内存管理优化
- 计算结果缓存

## 已知限制

1. **文件大小**: 建议不超过10MB
2. **数据量**: 建议不超过10万行
3. **浏览器内存**: 大数据集可能消耗较多内存
4. **统计方法**: 目前仅支持基础统计方法

## 未来计划

- [ ] 支持更多统计检验方法
- [ ] 添加多元回归分析
- [ ] 支持数据清洗功能
- [ ] 添加报告导出功能
- [ ] 支持更多图表类型
- [ ] 添加数据变换功能

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 项目讨论区

---

**注意**: 这是一个纯前端应用，所有数据处理都在浏览器本地完成，不会上传到任何服务器，确保数据安全和隐私。
