/**
 * 统计计算器
 * 版本: v2.0.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v1.0.0: 初始版本，实现描述统计和推断统计功能
 * - v2.0.0: 增强回归分析功能，添加详细统计指标和残差分析
 */

class StatisticsCalculator {
    constructor() {
        // 使用simple-statistics库进行计算
        this.ss = window.ss;
    }

    /**
     * 计算基本描述统计
     * @param {Array<number>} data - 数值数组
     * @returns {Object} 描述统计结果
     */
    calculateDescriptiveStats(data) {
        if (!data || data.length === 0) {
            throw new Error('数据为空');
        }

        const validData = data.filter(value => !isNaN(value) && value !== null && value !== undefined);
        
        if (validData.length === 0) {
            throw new Error('没有有效的数值数据');
        }

        try {
            return {
                count: validData.length,
                mean: this.ss.mean(validData),
                median: this.ss.median(validData),
                mode: this.ss.mode(validData),
                standardDeviation: this.ss.standardDeviation(validData),
                variance: this.ss.variance(validData),
                min: this.ss.min(validData),
                max: this.ss.max(validData),
                range: this.ss.max(validData) - this.ss.min(validData),
                quartiles: {
                    q1: this.ss.quantile(validData, 0.25),
                    q2: this.ss.quantile(validData, 0.5),
                    q3: this.ss.quantile(validData, 0.75)
                },
                skewness: this.ss.sampleSkewness(validData),
                kurtosis: this.ss.sampleKurtosis(validData),
                sum: this.ss.sum(validData)
            };
        } catch (error) {
            throw new Error('计算描述统计时出错: ' + error.message);
        }
    }

    /**
     * 计算频率分布
     * @param {Array<number>} data - 数值数组
     * @param {number} bins - 分组数量，默认为10
     * @returns {Object} 频率分布结果
     */
    calculateFrequencyDistribution(data, bins = 10) {
        if (!data || data.length === 0) {
            throw new Error('数据为空');
        }

        const validData = data.filter(value => !isNaN(value) && value !== null && value !== undefined);
        
        if (validData.length === 0) {
            throw new Error('没有有效的数值数据');
        }

        const min = this.ss.min(validData);
        const max = this.ss.max(validData);
        const binWidth = (max - min) / bins;
        
        const frequencies = new Array(bins).fill(0);
        const binRanges = [];
        
        // 创建分组范围
        for (let i = 0; i < bins; i++) {
            const start = min + i * binWidth;
            const end = min + (i + 1) * binWidth;
            binRanges.push({
                start: start,
                end: end,
                label: `${start.toFixed(2)} - ${end.toFixed(2)}`
            });
        }
        
        // 计算每个分组的频率
        validData.forEach(value => {
            let binIndex = Math.floor((value - min) / binWidth);
            if (binIndex >= bins) binIndex = bins - 1; // 处理边界情况
            frequencies[binIndex]++;
        });
        
        return {
            bins: bins,
            binWidth: binWidth,
            frequencies: frequencies,
            binRanges: binRanges,
            totalCount: validData.length
        };
    }

    /**
     * 计算相关系数
     * @param {Array<number>} x - X变量数组
     * @param {Array<number>} y - Y变量数组
     * @returns {Object} 相关分析结果
     */
    calculateCorrelation(x, y) {
        if (!x || !y || x.length === 0 || y.length === 0) {
            throw new Error('数据为空');
        }

        if (x.length !== y.length) {
            throw new Error('两个变量的数据长度不一致');
        }

        // 过滤掉任一变量为空值的数据点
        const validPairs = [];
        for (let i = 0; i < x.length; i++) {
            if (!isNaN(x[i]) && !isNaN(y[i]) && x[i] !== null && y[i] !== null && x[i] !== undefined && y[i] !== undefined) {
                validPairs.push([x[i], y[i]]);
            }
        }

        if (validPairs.length < 2) {
            throw new Error('有效数据点不足，无法计算相关系数');
        }

        const validX = validPairs.map(pair => pair[0]);
        const validY = validPairs.map(pair => pair[1]);

        try {
            const correlation = this.ss.sampleCorrelation(validX, validY);
            
            return {
                correlation: correlation,
                n: validPairs.length,
                interpretation: this.interpretCorrelation(correlation),
                strength: this.getCorrelationStrength(correlation)
            };
        } catch (error) {
            throw new Error('计算相关系数时出错: ' + error.message);
        }
    }

    /**
     * 执行单样本t检验
     * @param {Array<number>} data - 样本数据
     * @param {number} mu - 总体均值假设值
     * @param {number} alpha - 显著性水平，默认0.05
     * @returns {Object} t检验结果
     */
    performOneSampleTTest(data, mu, alpha = 0.05) {
        if (!data || data.length === 0) {
            throw new Error('数据为空');
        }

        const validData = data.filter(value => !isNaN(value) && value !== null && value !== undefined);

        if (validData.length < 2) {
            throw new Error('样本量不足，无法进行t检验');
        }

        try {
            const n = validData.length;
            const sampleMean = this.ss.mean(validData);
            const sampleStd = this.ss.standardDeviation(validData);
            const standardError = sampleStd / Math.sqrt(n);
            const tStatistic = (sampleMean - mu) / standardError;
            const degreesOfFreedom = n - 1;

            // 计算p值
            const pValue = this.calculateTTestPValue(Math.abs(tStatistic), degreesOfFreedom);

            // 计算置信区间
            const confidenceInterval = this.calculateConfidenceInterval(
                sampleMean, standardError, degreesOfFreedom, alpha
            );

            return {
                tStatistic: tStatistic,
                degreesOfFreedom: degreesOfFreedom,
                sampleMean: sampleMean,
                populationMean: mu,
                sampleStd: sampleStd,
                standardError: standardError,
                n: n,
                alpha: alpha,
                pValue: pValue,
                confidenceInterval: confidenceInterval,
                interpretation: this.interpretTTest(tStatistic, degreesOfFreedom, alpha)
            };
        } catch (error) {
            throw new Error('执行t检验时出错: ' + error.message);
        }
    }

    /**
     * 计算置信区间
     * @param {number} mean - 样本均值
     * @param {number} standardError - 标准误
     * @param {number} df - 自由度
     * @param {number} alpha - 显著性水平
     * @returns {Array} 置信区间[下限, 上限]
     */
    calculateConfidenceInterval(mean, standardError, df, alpha) {
        const tCritical = this.getTCriticalValue(df, alpha);
        const marginOfError = tCritical * standardError;

        return [
            mean - marginOfError,
            mean + marginOfError
        ];
    }

    /**
     * 执行简单线性回归
     * @param {Array<number>} x - 自变量数组
     * @param {Array<number>} y - 因变量数组
     * @returns {Object} 回归分析结果
     */
    performSimpleLinearRegression(x, y) {
        if (!x || !y || x.length === 0 || y.length === 0) {
            throw new Error('数据为空');
        }

        if (x.length !== y.length) {
            throw new Error('两个变量的数据长度不一致');
        }

        // 过滤掉任一变量为空值的数据点
        const validPairs = [];
        for (let i = 0; i < x.length; i++) {
            if (!isNaN(x[i]) && !isNaN(y[i]) && x[i] !== null && y[i] !== null && x[i] !== undefined && y[i] !== undefined) {
                validPairs.push([x[i], y[i]]);
            }
        }

        if (validPairs.length < 3) {
            throw new Error('有效数据点不足，无法进行回归分析');
        }

        try {
            const regression = this.ss.linearRegression(validPairs);
            const rSquared = this.ss.rSquared(validPairs, regression);

            const validX = validPairs.map(pair => pair[0]);
            const validY = validPairs.map(pair => pair[1]);
            const correlation = this.ss.sampleCorrelation(validX, validY);

            return {
                slope: regression.m,
                intercept: regression.b,
                equation: `y = ${regression.m.toFixed(4)}x + ${regression.b.toFixed(4)}`,
                rSquared: rSquared,
                correlation: correlation,
                n: validPairs.length,
                interpretation: this.interpretRegression(rSquared, correlation)
            };
        } catch (error) {
            throw new Error('执行回归分析时出错: ' + error.message);
        }
    }

    /**
     * 执行增强的多元线性回归分析
     * @param {Array<Array<number>>|Array<number>} x - 自变量数组（多元）或单个自变量数组（简单回归）
     * @param {Array<number>} y - 因变量数组
     * @param {Array<string>|string} xNames - 自变量名称数组或单个名称
     * @param {string} yName - 因变量名称
     * @returns {Object} 详细的回归分析结果
     */
    performEnhancedLinearRegression(x, y, xNames = 'X', yName = 'Y') {
        if (!x || !y || y.length === 0) {
            throw new Error('数据为空');
        }

        // 处理输入参数，支持简单回归和多元回归
        let xMatrix, variableNames;

        if (Array.isArray(x[0])) {
            // 多元回归：x是二维数组
            xMatrix = x;
            variableNames = Array.isArray(xNames) ? xNames : x.map((_, i) => `X${i+1}`);
        } else {
            // 简单回归：x是一维数组，转换为二维数组
            xMatrix = [x];
            variableNames = Array.isArray(xNames) ? xNames : [xNames];
        }

        const numVariables = xMatrix.length;
        const numObservations = y.length;

        // 验证数据长度一致性
        for (let i = 0; i < numVariables; i++) {
            if (xMatrix[i].length !== numObservations) {
                throw new Error(`自变量 ${variableNames[i]} 的数据长度与因变量不一致`);
            }
        }

        // 过滤掉包含空值的数据点
        const validIndices = [];
        for (let i = 0; i < numObservations; i++) {
            let isValid = !isNaN(y[i]) && y[i] !== null && y[i] !== undefined;

            for (let j = 0; j < numVariables && isValid; j++) {
                const value = xMatrix[j][i];
                isValid = !isNaN(value) && value !== null && value !== undefined;
            }

            if (isValid) {
                validIndices.push(i);
            }
        }

        if (validIndices.length < numVariables + 2) {
            throw new Error(`有效数据点不足，至少需要 ${numVariables + 2} 个有效数据点进行多元回归分析`);
        }

        try {
            // 提取有效数据
            const validY = validIndices.map(i => y[i]);
            const validXMatrix = xMatrix.map(xVar => validIndices.map(i => xVar[i]));
            const n = validIndices.length;

            // 构建设计矩阵 X (包含截距项)
            const X = [];
            for (let i = 0; i < n; i++) {
                const row = [1]; // 截距项
                for (let j = 0; j < numVariables; j++) {
                    row.push(validXMatrix[j][i]);
                }
                X.push(row);
            }

            // 使用矩阵运算计算回归系数 β = (X'X)^(-1)X'y
            const coefficients = this.calculateMultipleRegressionCoefficients(X, validY);

            // 计算拟合值和残差
            const fittedValues = [];
            for (let i = 0; i < n; i++) {
                let fitted = coefficients[0]; // 截距
                for (let j = 0; j < numVariables; j++) {
                    fitted += coefficients[j + 1] * validXMatrix[j][i];
                }
                fittedValues.push(fitted);
            }

            const residuals = validY.map((yi, i) => yi - fittedValues[i]);

            // 计算基本统计量
            const meanY = this.ss.mean(validY);

            // 计算平方和
            const SST = validY.reduce((sum, yi) => sum + Math.pow(yi - meanY, 2), 0);
            const SSE = residuals.reduce((sum, ri) => sum + ri * ri, 0);
            const SSR = SST - SSE;

            // 计算R²和调整R²
            const rSquared = SSR / SST;
            const adjustedRSquared = 1 - ((SSE / (n - numVariables - 1)) / (SST / (n - 1)));

            // 计算标准误
            const df = n - numVariables - 1; // 自由度
            const MSE = SSE / df;
            const standardError = Math.sqrt(MSE);

            // 计算系数的标准误、t值和p值
            const coefficientStats = this.calculateCoefficientStatistics(X, validY, coefficients, MSE);

            // F统计量
            const MSR = SSR / numVariables;
            const fStatistic = MSR / MSE;
            const fP = this.calculateFTestPValue(fStatistic, numVariables, df);

            // 残差分析
            const residualAnalysis = this.performResidualAnalysis(residuals, fittedValues, standardError);

            // 构建回归方程
            let equation = `${yName} = ${coefficients[0].toFixed(4)}`;
            for (let i = 0; i < numVariables; i++) {
                const coef = coefficients[i + 1];
                const sign = coef >= 0 ? ' + ' : ' - ';
                equation += `${sign}${Math.abs(coef).toFixed(4)} * ${variableNames[i]}`;
            }

            return {
                // 基本信息
                n: n,
                xNames: variableNames,
                yName: yName,
                equation: equation,
                numVariables: numVariables,

                // 系数信息
                coefficients: {
                    intercept: coefficientStats[0],
                    variables: coefficientStats.slice(1).map((stat, i) => ({
                        name: variableNames[i],
                        ...stat
                    }))
                },

                // 模型拟合度
                modelSummary: {
                    rSquared: rSquared,
                    adjustedRSquared: adjustedRSquared,
                    standardError: standardError,
                    fStatistic: fStatistic,
                    fPValue: fP,
                    degreesOfFreedom: df
                },

                // 方差分析
                anova: {
                    regression: {
                        df: numVariables,
                        sumOfSquares: SSR,
                        meanSquare: MSR,
                        fValue: fStatistic,
                        pValue: fP
                    },
                    residual: {
                        df: df,
                        sumOfSquares: SSE,
                        meanSquare: MSE
                    },
                    total: {
                        df: n - 1,
                        sumOfSquares: SST
                    }
                },

                // 残差分析
                residualAnalysis: residualAnalysis,

                // 原始数据
                data: {
                    x: validXMatrix,
                    y: validY,
                    fitted: fittedValues,
                    residuals: residuals
                },

                // 解释
                interpretation: this.interpretMultipleRegression(rSquared, adjustedRSquared, fP, coefficientStats, numVariables)
            };
        } catch (error) {
            throw new Error('执行增强回归分析时出错: ' + error.message);
        }
    }

    /**
     * 解释相关系数
     * @param {number} correlation - 相关系数
     * @returns {string} 解释文本
     */
    interpretCorrelation(correlation) {
        const abs = Math.abs(correlation);
        let strength = '';
        
        if (abs >= 0.9) strength = '非常强';
        else if (abs >= 0.7) strength = '强';
        else if (abs >= 0.5) strength = '中等';
        else if (abs >= 0.3) strength = '弱';
        else strength = '非常弱';
        
        const direction = correlation > 0 ? '正' : '负';
        
        return `${direction}相关，强度为${strength}（r = ${correlation.toFixed(3)}）`;
    }

    /**
     * 获取相关系数强度
     * @param {number} correlation - 相关系数
     * @returns {string} 强度等级
     */
    getCorrelationStrength(correlation) {
        const abs = Math.abs(correlation);
        
        if (abs >= 0.9) return 'very_strong';
        else if (abs >= 0.7) return 'strong';
        else if (abs >= 0.5) return 'moderate';
        else if (abs >= 0.3) return 'weak';
        else return 'very_weak';
    }

    /**
     * 解释t检验结果
     * @param {number} tStatistic - t统计量
     * @param {number} df - 自由度
     * @param {number} alpha - 显著性水平
     * @returns {string} 解释文本
     */
    interpretTTest(tStatistic, df, alpha) {
        // 简化的临界值判断（双尾检验）
        const criticalValue = this.getCriticalValue(df, alpha);
        const isSignificant = Math.abs(tStatistic) > criticalValue;
        
        return isSignificant 
            ? `在α = ${alpha}水平下，拒绝原假设，差异显著（|t| = ${Math.abs(tStatistic).toFixed(3)} > ${criticalValue.toFixed(3)}）`
            : `在α = ${alpha}水平下，接受原假设，差异不显著（|t| = ${Math.abs(tStatistic).toFixed(3)} ≤ ${criticalValue.toFixed(3)}）`;
    }

    /**
     * 解释回归分析结果
     * @param {number} rSquared - 决定系数
     * @param {number} correlation - 相关系数
     * @returns {string} 解释文本
     */
    interpretRegression(rSquared, correlation) {
        const percentage = (rSquared * 100).toFixed(1);
        const direction = correlation > 0 ? '正向' : '负向';
        
        let fit = '';
        if (rSquared >= 0.8) fit = '非常好';
        else if (rSquared >= 0.6) fit = '较好';
        else if (rSquared >= 0.4) fit = '一般';
        else fit = '较差';
        
        return `模型拟合度${fit}，${direction}关系，解释了${percentage}%的变异（R² = ${rSquared.toFixed(3)}）`;
    }

    /**
     * 执行残差分析
     * @param {Array<number>} residuals - 残差数组
     * @param {Array<number>} fittedValues - 拟合值数组
     * @param {number} standardError - 标准误
     * @returns {Object} 残差分析结果
     */
    performResidualAnalysis(residuals, fittedValues, standardError) {
        const n = residuals.length;

        // 标准化残差
        const standardizedResiduals = residuals.map(r => r / standardError);

        // 学生化残差（简化版本）
        const studentizedResiduals = residuals.map(r => r / standardError);

        // 残差统计量
        const residualMean = this.ss.mean(residuals);
        const residualStd = this.ss.standardDeviation(residuals);
        const residualMin = this.ss.min(residuals);
        const residualMax = this.ss.max(residuals);

        // Durbin-Watson统计量（检验自相关）
        let dwStatistic = 0;
        for (let i = 1; i < n; i++) {
            dwStatistic += Math.pow(residuals[i] - residuals[i-1], 2);
        }
        dwStatistic = dwStatistic / residuals.reduce((sum, r) => sum + r * r, 0);

        // 正态性检验（Shapiro-Wilk近似）
        const normalityTest = this.testResidualNormality(residuals);

        // 异常值检测
        const outliers = [];
        standardizedResiduals.forEach((sr, i) => {
            if (Math.abs(sr) > 2.5) {
                outliers.push({
                    index: i,
                    residual: residuals[i],
                    standardized: sr,
                    fitted: fittedValues[i]
                });
            }
        });

        return {
            residuals: residuals,
            standardizedResiduals: standardizedResiduals,
            studentizedResiduals: studentizedResiduals,
            statistics: {
                mean: residualMean,
                standardDeviation: residualStd,
                min: residualMin,
                max: residualMax,
                durbinWatson: dwStatistic
            },
            normalityTest: normalityTest,
            outliers: outliers,
            diagnostics: {
                hasOutliers: outliers.length > 0,
                normalityPValue: normalityTest.pValue,
                isNormal: normalityTest.pValue > 0.05,
                hasAutocorrelation: dwStatistic < 1.5 || dwStatistic > 2.5
            }
        };
    }

    /**
     * 检验残差正态性（简化的Shapiro-Wilk检验）
     * @param {Array<number>} residuals - 残差数组
     * @returns {Object} 正态性检验结果
     */
    testResidualNormality(residuals) {
        const n = residuals.length;
        const sortedResiduals = [...residuals].sort((a, b) => a - b);

        // 简化的正态性检验
        const mean = this.ss.mean(residuals);
        const std = this.ss.standardDeviation(residuals);

        // 计算偏度和峰度
        const skewness = this.ss.sampleSkewness(residuals);
        const kurtosis = this.ss.sampleKurtosis(residuals);

        // Jarque-Bera统计量
        const jbStatistic = (n / 6) * (skewness * skewness + (kurtosis - 3) * (kurtosis - 3) / 4);

        // 简化的p值计算
        let pValue = 0.5;
        if (Math.abs(skewness) > 1 || Math.abs(kurtosis - 3) > 2) {
            pValue = 0.01;
        } else if (Math.abs(skewness) > 0.5 || Math.abs(kurtosis - 3) > 1) {
            pValue = 0.1;
        }

        return {
            statistic: jbStatistic,
            pValue: pValue,
            skewness: skewness,
            kurtosis: kurtosis,
            isNormal: pValue > 0.05
        };
    }

    /**
     * 计算t检验的p值（改进版本）
     * @param {number} tValue - t统计量的绝对值
     * @param {number} df - 自由度
     * @returns {number} p值（双尾检验）
     */
    calculateTTestPValue(tValue, df) {
        // 扩展的临界值表，包含更多自由度和显著性水平
        const criticalValues = [
            { df: 1, t90: 6.314, t95: 12.706, t99: 63.657, t999: 636.619 },
            { df: 2, t90: 2.920, t95: 4.303, t99: 9.925, t999: 31.598 },
            { df: 3, t90: 2.353, t95: 3.182, t99: 5.841, t999: 12.924 },
            { df: 4, t90: 2.132, t95: 2.776, t99: 4.604, t999: 8.610 },
            { df: 5, t90: 2.015, t95: 2.571, t99: 4.032, t999: 6.869 },
            { df: 6, t90: 1.943, t95: 2.447, t99: 3.707, t999: 5.959 },
            { df: 7, t90: 1.895, t95: 2.365, t99: 3.499, t999: 5.408 },
            { df: 8, t90: 1.860, t95: 2.306, t99: 3.355, t999: 5.041 },
            { df: 9, t90: 1.833, t95: 2.262, t99: 3.250, t999: 4.781 },
            { df: 10, t90: 1.812, t95: 2.228, t99: 3.169, t999: 4.587 },
            { df: 15, t90: 1.753, t95: 2.131, t99: 2.947, t999: 4.073 },
            { df: 20, t90: 1.725, t95: 2.086, t99: 2.845, t999: 3.850 },
            { df: 25, t90: 1.708, t95: 2.060, t99: 2.787, t999: 3.725 },
            { df: 30, t90: 1.697, t95: 2.042, t99: 2.750, t999: 3.646 },
            { df: 40, t90: 1.684, t95: 2.021, t99: 2.704, t999: 3.551 },
            { df: 50, t90: 1.676, t95: 2.009, t99: 2.678, t999: 3.496 },
            { df: 60, t90: 1.671, t95: 2.000, t99: 2.660, t999: 3.460 },
            { df: 100, t90: 1.660, t95: 1.984, t99: 2.626, t999: 3.390 },
            { df: 1000, t90: 1.646, t95: 1.962, t99: 2.576, t999: 3.291 }
        ];

        // 找到最接近的自由度
        let closestDf = criticalValues[criticalValues.length - 1];
        for (let i = 0; i < criticalValues.length; i++) {
            if (df <= criticalValues[i].df) {
                closestDf = criticalValues[i];
                break;
            }
        }

        // 更精确的p值估计（双尾检验）
        if (tValue >= closestDf.t999) return 0.001;
        if (tValue >= closestDf.t99) return 0.01;
        if (tValue >= closestDf.t95) return 0.05;
        if (tValue >= closestDf.t90) return 0.10;

        // 对于较小的t值，使用线性插值估计
        if (tValue >= 1.0) {
            return Math.min(0.20, 0.10 + (closestDf.t90 - tValue) / closestDf.t90 * 0.10);
        }

        return Math.min(0.50, 0.20 + (1.0 - tValue) / 1.0 * 0.30);
    }

    /**
     * 计算F检验的p值（简化版本）
     * @param {number} fValue - F统计量
     * @param {number} df1 - 分子自由度
     * @param {number} df2 - 分母自由度
     * @returns {number} p值
     */
    calculateFTestPValue(fValue, df1, df2) {
        // 简化的F检验p值计算
        if (fValue >= 10) return 0.001;
        if (fValue >= 7) return 0.01;
        if (fValue >= 4) return 0.05;
        if (fValue >= 2.5) return 0.10;
        return 0.20;
    }

    /**
     * 获取t分布临界值
     * @param {number} df - 自由度
     * @param {number} alpha - 显著性水平
     * @returns {number} 临界值
     */
    getTCriticalValue(df, alpha) {
        return this.getCriticalValue(df, alpha);
    }

    /**
     * 获取t分布临界值（简化版本）
     * @param {number} df - 自由度
     * @param {number} alpha - 显著性水平
     * @returns {number} 临界值
     */
    getCriticalValue(df, alpha) {
        // 简化的临界值表（双尾检验，α = 0.05）
        const criticalValues = {
            1: 12.706, 2: 4.303, 3: 3.182, 4: 2.776, 5: 2.571,
            6: 2.447, 7: 2.365, 8: 2.306, 9: 2.262, 10: 2.228,
            15: 2.131, 20: 2.086, 25: 2.060, 30: 2.042, 40: 2.021,
            50: 2.009, 60: 2.000, 100: 1.984, 1000: 1.962
        };

        if (alpha !== 0.05) {
            // 对于非0.05的显著性水平，使用近似值
            return alpha === 0.01 ? 2.576 : 1.96;
        }

        // 查找最接近的自由度
        const availableDf = Object.keys(criticalValues).map(Number).sort((a, b) => a - b);
        let closestDf = availableDf[availableDf.length - 1]; // 默认使用最大值

        for (let i = 0; i < availableDf.length; i++) {
            if (df <= availableDf[i]) {
                closestDf = availableDf[i];
                break;
            }
        }

        return criticalValues[closestDf];
    }

    /**
     * 解释增强回归分析结果
     * @param {number} rSquared - 决定系数
     * @param {number} adjustedRSquared - 调整决定系数
     * @param {number} fPValue - F检验p值
     * @param {number} slopePValue - 斜率p值
     * @returns {string} 解释文本
     */
    interpretEnhancedRegression(rSquared, adjustedRSquared, fPValue, slopePValue) {
        const rSquaredPercent = (rSquared * 100).toFixed(1);
        const adjustedRSquaredPercent = (adjustedRSquared * 100).toFixed(1);

        let interpretation = `回归模型解释了因变量${rSquaredPercent}%的变异（R² = ${rSquared.toFixed(3)}），`;
        interpretation += `调整后的R²为${adjustedRSquaredPercent}%。`;

        if (fPValue < 0.05) {
            interpretation += `模型整体显著（F检验 p < 0.05），`;
        } else {
            interpretation += `模型整体不显著（F检验 p ≥ 0.05），`;
        }

        if (slopePValue < 0.05) {
            interpretation += `自变量对因变量有显著影响（p < 0.05）。`;
        } else {
            interpretation += `自变量对因变量无显著影响（p ≥ 0.05）。`;
        }

        return interpretation;
    }

    /**
     * 解释多元回归分析结果
     * @param {number} rSquared - R²值
     * @param {number} adjustedRSquared - 调整R²值
     * @param {number} fPValue - F检验p值
     * @param {Array<Object>} coefficientStats - 系数统计量
     * @param {number} numVariables - 自变量数量
     * @returns {Object} 解释结果
     */
    interpretMultipleRegression(rSquared, adjustedRSquared, fPValue, coefficientStats, numVariables) {
        const interpretation = {
            summary: '',
            modelSignificance: '',
            explanatoryPower: '',
            coefficientAnalysis: [],
            recommendations: [],
            warnings: []
        };

        // 模型整体显著性
        if (fPValue < 0.001) {
            interpretation.modelSignificance = '模型在统计上高度显著（p < 0.001），说明所选自变量整体上对因变量有显著的预测作用。';
        } else if (fPValue < 0.01) {
            interpretation.modelSignificance = '模型在统计上显著（p < 0.01），说明所选自变量整体上对因变量有显著的预测作用。';
        } else if (fPValue < 0.05) {
            interpretation.modelSignificance = '模型在统计上显著（p < 0.05），说明所选自变量整体上对因变量有显著的预测作用。';
        } else {
            interpretation.modelSignificance = '模型在统计上不显著（p ≥ 0.05），说明所选自变量整体上对因变量没有显著的预测作用。';
        }

        // 解释能力分析
        const rSquaredPercent = (rSquared * 100).toFixed(1);
        const adjustedRSquaredPercent = (adjustedRSquared * 100).toFixed(1);

        if (adjustedRSquared >= 0.7) {
            interpretation.explanatoryPower = `模型的解释能力很强，调整R² = ${adjustedRSquaredPercent}%，说明所选自变量能够解释因变量${adjustedRSquaredPercent}%的变异。`;
        } else if (adjustedRSquared >= 0.5) {
            interpretation.explanatoryPower = `模型的解释能力中等，调整R² = ${adjustedRSquaredPercent}%，说明所选自变量能够解释因变量${adjustedRSquaredPercent}%的变异。`;
        } else if (adjustedRSquared >= 0.3) {
            interpretation.explanatoryPower = `模型的解释能力较弱，调整R² = ${adjustedRSquaredPercent}%，说明所选自变量只能解释因变量${adjustedRSquaredPercent}%的变异。`;
        } else {
            interpretation.explanatoryPower = `模型的解释能力很弱，调整R² = ${adjustedRSquaredPercent}%，说明所选自变量只能解释因变量${adjustedRSquaredPercent}%的变异。`;
        }

        // 系数分析（跳过截距项）
        const variableStats = coefficientStats.slice(1);
        variableStats.forEach((stat, index) => {
            const analysis = {
                variable: `自变量${index + 1}`,
                significance: '',
                effect: '',
                interpretation: ''
            };

            // 显著性分析
            if (stat.pValue < 0.001) {
                analysis.significance = '高度显著（p < 0.001）';
            } else if (stat.pValue < 0.01) {
                analysis.significance = '显著（p < 0.01）';
            } else if (stat.pValue < 0.05) {
                analysis.significance = '显著（p < 0.05）';
            } else {
                analysis.significance = '不显著（p ≥ 0.05）';
            }

            // 效应方向和大小
            if (stat.estimate > 0) {
                analysis.effect = '正向影响';
                analysis.interpretation = `该变量每增加1个单位，因变量平均增加${Math.abs(stat.estimate).toFixed(4)}个单位。`;
            } else {
                analysis.effect = '负向影响';
                analysis.interpretation = `该变量每增加1个单位，因变量平均减少${Math.abs(stat.estimate).toFixed(4)}个单位。`;
            }

            interpretation.coefficientAnalysis.push(analysis);
        });

        // 建议
        if (fPValue < 0.05) {
            interpretation.recommendations.push('模型整体显著，可以用于预测和解释。');

            if (adjustedRSquared < 0.5) {
                interpretation.recommendations.push('考虑添加更多相关的自变量来提高模型的解释能力。');
            }

            const significantVars = variableStats.filter(stat => stat.pValue < 0.05).length;
            const totalVars = variableStats.length;

            if (significantVars < totalVars) {
                interpretation.recommendations.push(`有${totalVars - significantVars}个自变量不显著，考虑移除这些变量以简化模型。`);
            }
        } else {
            interpretation.recommendations.push('模型整体不显著，建议重新选择自变量或检查数据质量。');
        }

        // 警告
        if (numVariables > 1) {
            interpretation.warnings.push('多元回归中要注意多重共线性问题，确保自变量之间相关性不要过高。');
        }

        if (rSquared - adjustedRSquared > 0.1) {
            interpretation.warnings.push('R²与调整R²差异较大，可能存在过拟合，建议减少自变量数量。');
        }

        // 总结
        if (fPValue < 0.05 && adjustedRSquared >= 0.5) {
            interpretation.summary = `这是一个${numVariables > 1 ? '多元' : '简单'}线性回归模型，整体表现良好。模型在统计上显著，具有较强的解释能力。`;
        } else if (fPValue < 0.05) {
            interpretation.summary = `这是一个${numVariables > 1 ? '多元' : '简单'}线性回归模型，模型在统计上显著，但解释能力有限。`;
        } else {
            interpretation.summary = `这是一个${numVariables > 1 ? '多元' : '简单'}线性回归模型，但模型整体不显著，需要进一步改进。`;
        }

        return interpretation;
    }

    /**
     * 计算多元回归系数 β = (X'X)^(-1)X'y
     * @param {Array<Array<number>>} X - 设计矩阵
     * @param {Array<number>} y - 因变量向量
     * @returns {Array<number>} 回归系数向量
     */
    calculateMultipleRegressionCoefficients(X, y) {
        const n = X.length;
        const p = X[0].length;

        // 计算 X'X
        const XtX = this.matrixMultiply(this.matrixTranspose(X), X);

        // 计算 X'y
        const Xty = this.matrixVectorMultiply(this.matrixTranspose(X), y);

        // 计算 (X'X)^(-1)
        const XtXInverse = this.matrixInverse(XtX);

        // 计算 β = (X'X)^(-1)X'y
        const coefficients = this.matrixVectorMultiply(XtXInverse, Xty);

        return coefficients;
    }

    /**
     * 计算系数统计量（标准误、t值、p值、置信区间）
     * @param {Array<Array<number>>} X - 设计矩阵
     * @param {Array<number>} y - 因变量向量
     * @param {Array<number>} coefficients - 回归系数
     * @param {number} MSE - 均方误差
     * @returns {Array<Object>} 系数统计量数组
     */
    calculateCoefficientStatistics(X, y, coefficients, MSE) {
        const n = X.length;
        const p = X[0].length;
        const df = n - p;

        // 计算 (X'X)^(-1)
        const XtX = this.matrixMultiply(this.matrixTranspose(X), X);
        const XtXInverse = this.matrixInverse(XtX);

        const stats = [];

        for (let i = 0; i < p; i++) {
            // 标准误
            const standardError = Math.sqrt(MSE * XtXInverse[i][i]);

            // t值
            const tValue = coefficients[i] / standardError;

            // p值
            const pValue = this.calculateTTestPValue(Math.abs(tValue), df);

            // 95%置信区间
            const tCritical = this.getTCriticalValue(df, 0.05);
            const marginOfError = tCritical * standardError;
            const confidenceInterval = [
                coefficients[i] - marginOfError,
                coefficients[i] + marginOfError
            ];

            stats.push({
                estimate: coefficients[i],
                standardError: standardError,
                tValue: tValue,
                pValue: pValue,
                confidenceInterval: confidenceInterval
            });
        }

        return stats;
    }

    /**
     * 矩阵转置
     * @param {Array<Array<number>>} matrix - 输入矩阵
     * @returns {Array<Array<number>>} 转置矩阵
     */
    matrixTranspose(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const result = [];

        for (let j = 0; j < cols; j++) {
            result[j] = [];
            for (let i = 0; i < rows; i++) {
                result[j][i] = matrix[i][j];
            }
        }

        return result;
    }

    /**
     * 矩阵乘法
     * @param {Array<Array<number>>} A - 矩阵A
     * @param {Array<Array<number>>} B - 矩阵B
     * @returns {Array<Array<number>>} 乘积矩阵
     */
    matrixMultiply(A, B) {
        const rowsA = A.length;
        const colsA = A[0].length;
        const colsB = B[0].length;
        const result = [];

        for (let i = 0; i < rowsA; i++) {
            result[i] = [];
            for (let j = 0; j < colsB; j++) {
                let sum = 0;
                for (let k = 0; k < colsA; k++) {
                    sum += A[i][k] * B[k][j];
                }
                result[i][j] = sum;
            }
        }

        return result;
    }

    /**
     * 矩阵与向量乘法
     * @param {Array<Array<number>>} matrix - 矩阵
     * @param {Array<number>} vector - 向量
     * @returns {Array<number>} 结果向量
     */
    matrixVectorMultiply(matrix, vector) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const result = [];

        for (let i = 0; i < rows; i++) {
            let sum = 0;
            for (let j = 0; j < cols; j++) {
                sum += matrix[i][j] * vector[j];
            }
            result[i] = sum;
        }

        return result;
    }

    /**
     * 矩阵求逆（使用高斯-约旦消元法）
     * @param {Array<Array<number>>} matrix - 输入矩阵
     * @returns {Array<Array<number>>} 逆矩阵
     */
    matrixInverse(matrix) {
        const n = matrix.length;

        // 创建增广矩阵 [A|I]
        const augmented = [];
        for (let i = 0; i < n; i++) {
            augmented[i] = [...matrix[i]];
            for (let j = 0; j < n; j++) {
                augmented[i][n + j] = i === j ? 1 : 0;
            }
        }

        // 高斯-约旦消元
        for (let i = 0; i < n; i++) {
            // 寻找主元
            let maxRow = i;
            for (let k = i + 1; k < n; k++) {
                if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
                    maxRow = k;
                }
            }

            // 交换行
            if (maxRow !== i) {
                [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];
            }

            // 检查奇异矩阵
            if (Math.abs(augmented[i][i]) < 1e-10) {
                throw new Error('矩阵奇异，无法求逆');
            }

            // 将主元归一化
            const pivot = augmented[i][i];
            for (let j = 0; j < 2 * n; j++) {
                augmented[i][j] /= pivot;
            }

            // 消元
            for (let k = 0; k < n; k++) {
                if (k !== i) {
                    const factor = augmented[k][i];
                    for (let j = 0; j < 2 * n; j++) {
                        augmented[k][j] -= factor * augmented[i][j];
                    }
                }
            }
        }

        // 提取逆矩阵
        const inverse = [];
        for (let i = 0; i < n; i++) {
            inverse[i] = augmented[i].slice(n);
        }

        return inverse;
    }
}

// 导出统计计算器实例
window.statisticsCalculator = new StatisticsCalculator();
