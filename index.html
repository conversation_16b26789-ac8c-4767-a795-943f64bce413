<!DOCTYPE html>
<!--
版本: v3.0.0
创建日期: 2025-07-19
最后更新: 2025-07-30
变更记录:
- v1.0.0: 初始版本，创建基础HTML结构和TailwindCSS配置
- v2.0.0: 界面重构，采用单列垂直布局，集成所有功能模块
- v3.0.0: 项目代码清理和优化，删除多余文件和调试代码
-->
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回归分析工具 - 沈浪</title>

    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Plotly.js CDN -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <!-- SheetJS CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Simple Statistics CDN -->
    <script src="https://unpkg.com/simple-statistics@7.8.3/dist/simple-statistics.min.js"></script>

    <style>
        /* 紧凑化布局优化样式 */

        /* 全局间距优化 */
        .compact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.75rem;
        }

        .compact-section {
            margin-bottom: 0.75rem;
        }

        .compact-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 0.875rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        /* 文件上传区域优化 */
        .file-drop-zone {
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.2s ease;
            background: #fafafa;
        }
        .file-drop-zone.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }

        /* 步骤指示器优化 */
        .step-indicator {
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        /* 表格优化 */
        .compact-table {
            font-size: 0.875rem;
        }
        .compact-table th,
        .compact-table td {
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        /* 数据预览表格优化 */
        .data-preview-table {
            font-size: 12px;
            border-collapse: collapse;
            width: 100%;
            max-width: 100%;
        }
        .data-preview-table th,
        .data-preview-table td {
            width: 120px;
            min-width: 120px;
            max-width: 120px;
            height: 32px;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .data-preview-table th {
            background-color: #f9fafb;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .data-preview-container {
            max-height: 200px;
            overflow: auto;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
        }

        /* 列信息表格样式 */
        .column-info-container {
            max-height: 200px;
            overflow: auto;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }

        .column-info-table {
            font-size: 12px;
            border-collapse: collapse;
            width: 100%;
            min-width: 720px; /* 确保所有列可见 */
        }

        .column-info-table th,
        .column-info-table td {
            height: 32px;
            padding: 4px 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: left;
        }

        .column-info-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .column-info-table tbody tr:nth-child(even) {
            background-color: #f9fafb;
        }

        .column-info-table tbody tr:hover {
            background-color: #f3f4f6;
        }

        /* 数据质量评分样式 */
        .quality-excellent {
            color: #059669;
            font-weight: 600;
        }

        .quality-good {
            color: #0891b2;
            font-weight: 600;
        }

        .quality-fair {
            color: #d97706;
            font-weight: 600;
        }

        .quality-poor {
            color: #dc2626;
            font-weight: 600;
        }

        /* 缺失数据样式 */
        .missing-data {
            color: #dc2626;
            font-weight: 500;
        }

        .complete-data {
            color: #059669;
            font-weight: 500;
        }






















        /* 相关性可视化样式 */
        .correlation-visualization-container {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .correlation-chart-container {
            position: relative;
            height: 400px;
            margin: 1rem 0;
        }

        .correlation-chart-container.mb-6 {
            margin-bottom: 2rem;
        }

        .correlation-heatmap-container {
            position: relative;
            height: 500px;
            margin: 1rem 0;
        }

        .correlation-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .correlation-summary-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 0.75rem;
            text-align: center;
        }

        .correlation-summary-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1f2937;
        }

        .correlation-summary-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        /* 标签页样式 */
        .correlation-tabs {
            display: flex;
            border-bottom: 2px solid #e5e7eb;
            margin-bottom: 1rem;
        }

        .correlation-tab {
            padding: 0.75rem 1.5rem;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }

        .correlation-tab.active {
            background: white;
            color: #1f2937;
            border-bottom: 2px solid white;
            margin-bottom: -2px;
        }

        .correlation-tab:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .correlation-tab-content {
            display: none;
        }

        .correlation-tab-content.active {
            display: block;
        }

        /* 热力图图例样式 */
        .heatmap-legend {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 1px solid #d1d5db;
        }

        .legend-label {
            font-size: 0.875rem;
            color: #374151;
        }

        /* 矩阵热力图样式 */
        .matrix-heatmap-wrapper {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            overflow: auto;
            min-height: 400px;
            max-height: 80vh;
        }

        .matrix-heatmap-container {
            /* 样式现在通过JavaScript动态设置 */
            flex-shrink: 0;
        }

        .matrix-cell {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        .matrix-cell:hover {
            outline: 2px solid #374151;
            outline-offset: -2px;
        }

        .y-axis-label, .x-axis-label {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        .y-axis-label:hover, .x-axis-label:hover {
            color: #1f2937;
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .correlation-chart-container {
                height: 300px;
            }

            .correlation-summary {
                grid-template-columns: 1fr;
            }

            .matrix-heatmap-wrapper {
                padding: 10px;
                overflow: auto;
                max-width: 100vw;
                max-height: 70vh;
                min-height: 300px;
                align-items: flex-start;
            }

            .matrix-heatmap-container {
                min-width: 300px;
                transform-origin: top left;
                flex-shrink: 0;
            }

            .y-axis-label, .x-axis-label {
                font-size: 10px !important;
            }

            .matrix-cell {
                font-size: 8px !important;
            }
        }

        @media (max-width: 480px) {
            .matrix-heatmap-wrapper {
                min-height: 250px;
                max-height: 60vh;
                padding: 5px;
                overflow: auto;
            }

            .matrix-heatmap-container {
                transform: scale(0.7);
                transform-origin: top left;
                min-width: 250px;
            }

            .correlation-tabs {
                flex-direction: column;
            }

            .correlation-tab {
                text-align: center;
                border-bottom: 1px solid #e5e7eb;
                border-right: none;
            }

            .correlation-tab.active {
                border-bottom: 2px solid #3b82f6;
                margin-bottom: 0;
            }
        }

        /* 水平统计表格 */
        .horizontal-stats-table {
            font-size: 12px;
            border-collapse: collapse;
            width: 100%;
            min-width: 800px;
        }
        .horizontal-stats-table th,
        .horizontal-stats-table td {
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            text-align: center;
            white-space: nowrap;
        }
        .horizontal-stats-table th {
            background-color: #f3f4f6;
            font-weight: 600;
        }
        .horizontal-stats-table .variable-name {
            background-color: #f9fafb;
            font-weight: 600;
            text-align: left;
            min-width: 100px;
        }
        .stats-table-container {
            overflow-x: auto;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
        }

        /* 简化文件上传区域 */
        .compact-file-drop {
            border: 2px dashed #d1d5db;
            border-radius: 0.375rem;
            padding: 0.75rem;
            text-align: center;
            transition: all 0.2s ease;
            background: #fafafa;
            min-height: 64px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .compact-file-drop.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }

        /* 简洁文件信息 */
        .compact-file-info {
            display: flex;
            align-items: center;
            padding: 0.375rem 0.5rem;
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 0.25rem;
            font-size: 0.8125rem;
            line-height: 1.25;
        }

        /* 移动端优化 */
        @media (max-width: 640px) {
            .compact-file-drop {
                padding: 0.625rem;
                min-height: 56px;
            }

            .compact-file-info {
                padding: 0.25rem 0.375rem;
                font-size: 0.75rem;
            }

            .compact-file-info span {
                font-size: 0.75rem;
            }
        }

        /* 按钮组优化 */
        .button-group {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            align-items: center;
        }
        .compact-button {
            padding: 0.625rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 2.5rem;
        }

        /* 变量选择优化 */
        .variable-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 0.5rem;
        }
        .variable-item {
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .variable-item:hover {
            background-color: #f9fafb;
            border-color: #d1d5db;
        }

        /* 变量选择区域滚动条优化 */
        #x-variables::-webkit-scrollbar,
        #y-variables::-webkit-scrollbar {
            width: 6px;
        }

        #x-variables::-webkit-scrollbar-track,
        #y-variables::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        #x-variables::-webkit-scrollbar-thumb,
        #y-variables::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        #x-variables::-webkit-scrollbar-thumb:hover,
        #y-variables::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 变量选择状态样式 */
        .variable-selected {
            background-color: #dbeafe;
            border-color: #3b82f6;
        }

        .variable-disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        /* 变量类型标识样式 */
        .variable-type-badge {
            display: inline-block;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            line-height: 1;
        }

        /* 拖拽功能样式 */
        .variable-item[draggable="true"] {
            cursor: grab;
        }

        .variable-item[draggable="true"]:active {
            cursor: grabbing;
        }

        .variable-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .drop-zone {
            position: relative;
        }

        .drop-zone.drag-over {
            background-color: #f0f9ff;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
        }

        .drop-zone::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px dashed transparent;
            border-radius: 0.375rem;
            pointer-events: none;
            transition: all 0.2s ease;
        }

        .drop-zone.drag-over::before {
            border-color: #0ea5e9;
            background-color: rgba(14, 165, 233, 0.05);
        }

        /* 图表容器优化 */
        .chart-container {
            position: relative;
            height: 280px;
            margin-bottom: 1rem;
        }

        @media (min-width: 768px) {
            .compact-container {
                padding: 1rem;
            }
            .compact-section {
                margin-bottom: 1.25rem;
            }
            .compact-card {
                padding: 1.25rem;
            }
            .chart-container {
                height: 320px;
            }
        }

        @media (min-width: 1024px) {
            .chart-container {
                height: 350px;
            }
        }

        /* 信息提示优化 */
        .info-box {
            padding: 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        /* 隐藏状态优化 */
        .hidden {
            display: none !important;
        }

        /* 加载状态优化 */
        .loading-spinner {
            width: 2rem;
            height: 2rem;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .compact-section {
            margin-bottom: 1rem;
        }

        @media (min-width: 768px) {
            .compact-section {
                margin-bottom: 1.5rem;
            }
        }

        /* 滚动优化 */
        .scroll-smooth {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen scroll-smooth">
    <!-- 紧凑头部 -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="compact-container">
            <div class="flex justify-between items-center py-2.5">
                <h1 class="text-lg sm:text-xl font-bold text-gray-900">回归分析工具</h1>
                <div class="text-xs text-gray-500">
                    统计分析
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 - 紧凑布局 -->
    <main class="compact-container py-3">
        <!-- 1. 数据导入区域 -->
        <section id="data-import" class="compact-section">
            <div class="compact-card">
                <div class="flex items-center mb-1.5">
                    <div class="step-indicator bg-blue-100 text-blue-600">1</div>
                    <h2 class="ml-2 text-base font-semibold text-gray-900">数据导入</h2>
                </div>

                <!-- 文件上传区域 -->
                <div id="file-drop-zone" class="compact-file-drop">
                    <div class="flex items-center justify-center space-x-2.5">
                        <svg class="h-5 w-5 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="text-center">
                            <p class="text-sm text-gray-600">拖拽Excel文件或</p>
                            <button id="file-select-btn" class="compact-button bg-blue-600 text-white hover:bg-blue-700 mt-0.5 text-sm py-1.5 px-3">
                                选择文件
                            </button>
                            <input type="file" id="file-input" accept=".xlsx,.xls" class="hidden">
                        </div>
                    </div>
                </div>

                <!-- 文件信息显示 -->
                <div id="file-info" class="mt-1.5 hidden">
                    <div class="compact-file-info text-blue-800">
                        <svg class="h-3.5 w-3.5 text-blue-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="ml-1.5 font-medium text-sm" id="file-name"></span>
                        <span class="ml-1.5 text-xs" id="file-details"></span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 2. 数据概览区域 -->
        <section id="data-overview" class="compact-section hidden">
            <div class="compact-card">
                <div class="flex items-center mb-2">
                    <div class="step-indicator bg-green-100 text-green-600">2</div>
                    <h2 class="ml-2 text-base font-semibold text-gray-900">数据概览</h2>
                </div>



                <!-- 列信息 -->
                <div id="column-info" class="mb-3">
                    <!-- 列信息将在这里显示 -->
                </div>

                <!-- 数据预览 -->
                <div class="border-t pt-3">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">数据预览（前5行）</h3>
                    <div id="data-table-container">
                        <!-- 数据表格将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 3. 变量选择区域 -->
        <section id="variable-selection" class="compact-section hidden">
            <div class="compact-card">
                <div class="flex items-center mb-2">
                    <div class="step-indicator bg-indigo-100 text-indigo-600">3</div>
                    <h2 class="ml-2 text-base font-semibold text-gray-900">变量选择</h2>
                </div>

                <!-- 变量选择说明 -->
                <div class="info-box bg-blue-50 border border-blue-200 text-blue-800">
                    <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    请选择要分析的变量。对于回归分析，需要选择自变量(X)和因变量(Y)。
                </div>

                <!-- 变量列表 -->
                <div id="variable-list" class="mb-3">
                    <!-- 变量选择项将在这里动态生成 -->
                </div>

                <!-- 回归分析变量选择 -->
                <div id="regression-variables" class="info-box bg-gray-50 border border-gray-200 text-gray-800 hidden">
                    <h4 class="font-medium mb-2">多元线性回归分析设置</h4>

                    <!-- 功能说明 -->
                    <div class="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <p class="text-sm text-blue-800">
                            <strong>💡 提示：</strong>可以选择多个自变量进行多元回归分析。当只选择一个自变量时，将进行简单线性回归。
                        </p>
                    </div>

                    <!-- 变量选择预览 -->
                    <div id="variable-preview" class="mb-3 p-2 bg-blue-50 border border-blue-200 rounded-md hidden">
                        <div class="text-sm text-blue-800">
                            <div id="x-variables-preview" class="mb-1">
                                <span class="font-medium">自变量:</span> <span id="x-variables-list">未选择</span>
                            </div>
                            <div id="y-variables-preview">
                                <span class="font-medium">因变量:</span> <span id="y-variables-list">未选择</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium mb-1">
                                自变量 (X) - 可选择多个
                                <span id="x-count" class="text-xs text-gray-500">(0个已选)</span>
                            </label>
                            <div id="x-variables" class="drop-zone space-y-1 max-h-60 overflow-y-auto border border-gray-200 rounded-md p-2 bg-white"
                                 data-variable-type="x">
                                <!-- 自变量选择项 -->
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">
                                因变量 (Y) - 选择一个
                                <span id="y-count" class="text-xs text-gray-500">(0个已选)</span>
                            </label>
                            <div id="y-variables" class="drop-zone space-y-1 max-h-60 overflow-y-auto border border-gray-200 rounded-md p-2 bg-white"
                                 data-variable-type="y">
                                <!-- 因变量选择项 -->
                            </div>
                        </div>
                    </div>
                    <div id="regression-validation" class="mt-2 text-sm text-red-600 hidden">
                        请至少选择一个自变量和一个因变量进行回归分析
                    </div>
                </div>
            </div>
        </section>

        <!-- 4. 分析选项区域 -->
        <section id="analysis-options" class="compact-section hidden">
            <div class="compact-card">
                <div class="flex items-center mb-3">
                    <div class="step-indicator bg-purple-100 text-purple-600">4</div>
                    <h2 class="ml-2 text-base font-semibold text-gray-900">分析选项</h2>
                </div>



                <!-- 分析选项 -->
                <div class="space-y-2">
                    <!-- 统计分析选项 - 紧凑布局 -->
                    <div class="border rounded-lg p-3">
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                            <!-- 基本统计量 -->
                            <label class="flex items-start p-2 rounded hover:bg-gray-50 cursor-pointer border border-gray-100">
                                <input type="checkbox" id="basic-stats" class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                <div class="ml-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 mr-1 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">基本统计量</span>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-0.5">均值、中位数、标准差等</p>
                                </div>
                            </label>

                            <!-- 频率分布 -->
                            <label class="flex items-start p-2 rounded hover:bg-gray-50 cursor-pointer border border-gray-100">
                                <input type="checkbox" id="frequency-dist" class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div class="ml-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">频率分布</span>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-0.5">直方图和分布分析</p>
                                </div>
                            </label>

                            <!-- 相关性可视化 -->
                            <label class="flex items-start p-2 rounded hover:bg-gray-50 cursor-pointer border border-gray-100">
                                <input type="checkbox" id="correlation" class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div class="ml-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 mr-1 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">相关性可视化</span>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-0.5">散点图和相关性图表</p>
                                </div>
                            </label>

                            <!-- t检验 -->
                            <label class="flex items-start p-2 rounded hover:bg-gray-50 cursor-pointer border border-gray-100">
                                <input type="checkbox" id="t-test" class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div class="ml-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 mr-1 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">t检验</span>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-0.5">单样本检验</p>
                                </div>
                            </label>

                            <!-- 多元回归 -->
                            <label class="flex items-start p-2 rounded hover:bg-gray-50 cursor-pointer border border-gray-100">
                                <input type="checkbox" id="regression" class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div class="ml-2">
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 mr-1 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-700">多元回归</span>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-0.5">多元线性回归分析</p>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="button-group mt-4">
                    <button id="run-analysis" class="compact-button bg-green-600 text-white hover:bg-green-700 flex-1 flex items-center justify-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        运行选中分析
                    </button>
                    <button id="clear-results" class="compact-button bg-gray-500 text-white hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        清空结果
                    </button>
                </div>
            </div>
        </section>

        <!-- 5. 分析结果区域 -->
        <section id="analysis-results" class="compact-section hidden">
            <div class="compact-card">
                <div class="flex items-center mb-2">
                    <div class="step-indicator bg-orange-100 text-orange-600">5</div>
                    <h2 class="ml-2 text-base font-semibold text-gray-900">分析结果</h2>
                </div>
                <div id="results-container" class="space-y-3">
                    <!-- 分析结果将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 返回顶部按钮 -->
        <div class="fixed bottom-3 right-3 z-40">
            <button id="back-to-top" class="hidden bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
            </button>
        </div>
    </main>

    <!-- JavaScript 模块 -->
    <script src="src/utils/dataManager.js"></script>
    <script src="src/utils/statisticsCalculator.js"></script>
    <script src="src/utils/tTestExplainer.js"></script>
    <script src="src/utils/chartRenderer.js"></script>
    <script src="src/app.js"></script>

    <!-- 返回顶部功能 -->
    <script>
        // 返回顶部按钮功能
        const backToTopBtn = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('hidden');
            } else {
                backToTopBtn.classList.add('hidden');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
