/**
 * t检验结果解释器
 * 版本: v1.0.0
 * 创建日期: 2025-07-20
 * 变更记录:
 * - v1.0.0: 初始版本，提供国际化术语对照和用户友好的业务解释功能
 */

class TTestExplainer {
    constructor() {
        // 国际化术语对照表
        this.terminology = {
            sampleMean: '样本均值 (Sample Mean)',
            hypothesizedMean: '假设总体均值 (Hypothesized Population Mean)',
            sampleStd: '样本标准差 (Sample Standard Deviation)',
            standardError: '标准误 (Standard Error)',
            tStatistic: 't统计量 (t-Statistic)',
            pValue: 'p值 (p-value)',
            degreesOfFreedom: '自由度 (Degrees of Freedom)',
            confidenceInterval: '置信区间 (Confidence Interval)',
            significanceLevel: '显著性水平 (Significance Level)',
            sampleSize: '样本量 (Sample Size)'
        };

        // 显著性水平阈值
        this.significanceThresholds = {
            highly_significant: 0.001,
            very_significant: 0.01,
            significant: 0.05,
            marginally_significant: 0.10
        };
    }

    /**
     * 生成业务解释
     * @param {Object} tTestResult - t检验结果对象
     * @param {string} fieldName - 数据字段名称
     * @returns {Object} 包含业务解释的对象
     */
    generateBusinessExplanation(tTestResult, fieldName) {
        const {
            tStatistic,
            sampleMean,
            populationMean,
            sampleStd,
            n,
            alpha,
            pValue,
            confidenceInterval
        } = tTestResult;

        const isSignificant = pValue < alpha;
        const direction = sampleMean > populationMean ? '高于' : '低于';
        const confidenceLevel = ((1 - alpha) * 100).toFixed(0);
        const difference = Math.abs(sampleMean - populationMean);
        const percentageDiff = ((difference / populationMean) * 100).toFixed(1);

        return {
            summary: this.generateSummary(fieldName, sampleMean, populationMean, isSignificant, direction),
            practicalMeaning: this.generatePracticalMeaning(fieldName, sampleMean, populationMean, difference, percentageDiff, isSignificant),
            confidenceExplanation: this.generateConfidenceExplanation(fieldName, confidenceInterval, confidenceLevel),
            reliabilityAssessment: this.generateReliabilityAssessment(n, pValue, isSignificant),
            actionRecommendation: this.generateActionRecommendation(fieldName, isSignificant, direction, difference, n)
        };
    }

    /**
     * 生成概要说明
     */
    generateSummary(fieldName, sampleMean, populationMean, isSignificant, direction) {
        if (isSignificant) {
            return `经过统计分析，我们发现"${fieldName}"的实际平均值（${sampleMean.toFixed(2)}）与预期值（${populationMean}）存在显著差异。数据显示实际值${direction}预期值，这种差异在统计上是可信的，不太可能是由随机因素造成的。`;
        } else {
            return `经过统计分析，我们发现"${fieldName}"的实际平均值（${sampleMean.toFixed(2)}）与预期值（${populationMean}）之间的差异在统计上不显著。这意味着观察到的差异可能是由正常的数据波动造成的。`;
        }
    }

    /**
     * 生成实际意义解释
     */
    generatePracticalMeaning(fieldName, sampleMean, populationMean, difference, percentageDiff, isSignificant) {
        if (isSignificant) {
            return `从业务角度来看，"${fieldName}"的实际值与预期值相差${difference.toFixed(2)}个单位，相对差异约为${percentageDiff}%。这个差异具有实际意义，建议进一步分析造成这种差异的原因。`;
        } else {
            return `从业务角度来看，虽然"${fieldName}"的实际值与预期值有${difference.toFixed(2)}个单位的差异（约${percentageDiff}%），但这个差异在可接受的范围内，可能是正常的业务波动。`;
        }
    }

    /**
     * 生成置信区间解释
     */
    generateConfidenceExplanation(fieldName, confidenceInterval, confidenceLevel) {
        if (!confidenceInterval || confidenceInterval.length !== 2) {
            return '置信区间计算不可用。';
        }

        const [lower, upper] = confidenceInterval;
        const range = upper - lower;
        
        return `我们有${confidenceLevel}%的把握认为"${fieldName}"的真实平均值在${lower.toFixed(2)}到${upper.toFixed(2)}之间。这个区间的宽度为${range.toFixed(2)}，区间越窄说明我们的估计越精确。`;
    }

    /**
     * 生成可靠性评估
     */
    generateReliabilityAssessment(n, pValue, isSignificant) {
        let sampleSizeAssessment = '';
        if (n < 30) {
            sampleSizeAssessment = '样本量较小，建议收集更多数据以提高结论的可靠性。';
        } else if (n < 100) {
            sampleSizeAssessment = '样本量适中，结论具有一定的可靠性。';
        } else {
            sampleSizeAssessment = '样本量充足，结论具有较高的可靠性。';
        }

        let pValueAssessment = '';
        if (pValue < 0.001) {
            pValueAssessment = '证据非常强烈，结论高度可信。';
        } else if (pValue < 0.01) {
            pValueAssessment = '证据很强，结论很可信。';
        } else if (pValue < 0.05) {
            pValueAssessment = '证据充分，结论可信。';
        } else if (pValue < 0.10) {
            pValueAssessment = '证据较弱，结论需要谨慎对待。';
        } else {
            pValueAssessment = '证据不足，无法得出明确结论。';
        }

        return `基于${n}个样本的分析，${sampleSizeAssessment}从统计显著性来看，${pValueAssessment}`;
    }

    /**
     * 生成行动建议
     */
    generateActionRecommendation(fieldName, isSignificant, direction, difference, n) {
        if (isSignificant) {
            if (n < 30) {
                return `建议：1）收集更多"${fieldName}"的数据以验证当前发现；2）调查造成差异的具体原因；3）评估这种差异对业务的影响程度。`;
            } else {
                return `建议：1）深入分析造成"${fieldName}"差异的根本原因；2）评估是否需要调整预期目标或业务策略；3）持续监控该指标的变化趋势。`;
            }
        } else {
            if (n < 30) {
                return `建议：1）增加样本量以获得更可靠的结论；2）继续监控"${fieldName}"的表现；3）如有必要，可以调整分析方法或收集更多相关数据。`;
            } else {
                return `建议：1）当前"${fieldName}"表现符合预期，可以维持现状；2）建立定期监控机制；3）关注可能影响该指标的外部因素变化。`;
            }
        }
    }

    /**
     * 生成HTML格式的业务解释
     * @param {Object} businessExplanation - 业务解释对象
     * @returns {string} HTML格式的业务解释
     */
    generateBusinessHTML(businessExplanation) {
        return `
            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h5 class="font-semibold text-green-800 mb-3">📊 业务解释说明</h5>
                
                <div class="space-y-3 text-sm text-green-700">
                    <div>
                        <strong>📋 结果概要：</strong>
                        <p class="mt-1">${businessExplanation.summary}</p>
                    </div>
                    
                    <div>
                        <strong>💼 实际意义：</strong>
                        <p class="mt-1">${businessExplanation.practicalMeaning}</p>
                    </div>
                    
                    <div>
                        <strong>📏 可信度评估：</strong>
                        <p class="mt-1">${businessExplanation.confidenceExplanation}</p>
                    </div>
                    
                    <div>
                        <strong>🔍 可靠性分析：</strong>
                        <p class="mt-1">${businessExplanation.reliabilityAssessment}</p>
                    </div>
                    
                    <div class="pt-2 border-t border-green-300">
                        <strong>💡 行动建议：</strong>
                        <p class="mt-1 font-medium">${businessExplanation.actionRecommendation}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取国际化术语
     * @param {string} key - 术语键名
     * @returns {string} 中英文对照术语
     */
    getTerm(key) {
        return this.terminology[key] || key;
    }
}

// 导出类供其他模块使用
window.TTestExplainer = TTestExplainer;
